{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\context\\\\timeListenerContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useRef, useState } from \"react\";\nimport { useSounds } from \"./soundContext\";\nimport { useSearchParams } from \"react-router-dom\";\nimport { useAppDispatch, useAppSelector } from \"../app/store\";\nimport { setIsInputDisabled } from \"../app/store/slices/gameSlice\";\nimport { gameApi } from \"../shared/services\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TimeStartContext = /*#__PURE__*/createContext(undefined);\nexport const TimeStartProvider = ({\n  roomId,\n  children\n}) => {\n  _s();\n  // const {setAnimationKey} = useHost();\n  const dispatch = useAppDispatch();\n  const {} = useAppSelector();\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [timeElapsed, settimeElapsed] = useState(0);\n  const [playerAnswerTime, setPlayerAnswerTime] = useState(0);\n  const timerRef = useRef(null);\n  const sounds = useSounds();\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n  const roundRef = useRef(round);\n  const {\n    submitAnswer\n  } = gameApi;\n  const handleTimeEnd = async () => {\n    console.log(\"Time is up!\");\n    dispatch(setIsInputDisabled(true));\n    await submitAnswer({\n      answer: \"test\",\n      stt: \"test\",\n      time: 1,\n      player_name: \"test\",\n      avatar: \"test\"\n    }, roomId);\n  };\n\n  // Watch for timeLeft reaching 0\n  useEffect(() => {\n    if (timeLeft === 0) {\n      handleTimeEnd();\n    }\n  }, [timeLeft]);\n  const startTimer = async duration => {\n    // Clear any existing timer\n    if (timerRef.current) clearInterval(timerRef.current);\n    console.log(\"duration\", duration);\n    const durationInMs = duration * 1000;\n    // Set the new time\n    setTimeLeft(durationInMs / 1000);\n    const startTime = Date.now();\n\n    // Start the countdown\n    timerRef.current = setInterval(() => {\n      const elapsedMs = Date.now() - startTime;\n      const remainingMs = Math.max(durationInMs - elapsedMs, 0);\n      const remainingSec = remainingMs / 1000;\n      const timeElapsed = duration - remainingSec;\n      console.log(\"timeElapsed\", parseFloat(timeElapsed.toFixed(2)), \"s\");\n      settimeElapsed(parseFloat(timeElapsed.toFixed(2)));\n      setTimeLeft(prev => {\n        console.log(prev);\n        if (prev <= 1) {\n          // setAnimationKey((prev: number) => prev + 1);\n          clearInterval(timerRef.current);\n          return 0;\n        }\n        return prev - 50 / 1000;\n      });\n    }, 50);\n  };\n  const setExternalTimer = seconds => {\n    startTimer(seconds);\n  };\n  const isInitialMount = useRef(true);\n  return /*#__PURE__*/_jsxDEV(TimeStartContext.Provider, {\n    value: {\n      timeLeft,\n      timeElapsed,\n      playerAnswerTime,\n      setPlayerAnswerTime,\n      setTimeLeft,\n      startTimer,\n      setExternalTimer\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(TimeStartProvider, \"ePvkJ1m1CYJ4KwrZUtU3L//qu/E=\", false, function () {\n  return [useAppDispatch, useAppSelector, useSounds, useSearchParams];\n});\n_c = TimeStartProvider;\nexport const useTimeStart = () => {\n  _s2();\n  const context = useContext(TimeStartContext);\n  if (!context) {\n    throw new Error(\"useTimeStart must be used within a TimeStartProvider\");\n  }\n  return context;\n};\n_s2(useTimeStart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TimeStartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useRef", "useState", "useSounds", "useSearchParams", "useAppDispatch", "useAppSelector", "setIsInputDisabled", "gameApi", "jsxDEV", "_jsxDEV", "TimeStartContext", "undefined", "TimeStartProvider", "roomId", "children", "_s", "dispatch", "timeLeft", "setTimeLeft", "timeElapsed", "settimeElapsed", "playerAnswerTime", "setPlayerAnswerTime", "timerRef", "sounds", "searchParams", "round", "get", "roundRef", "submitAnswer", "handleTimeEnd", "console", "log", "answer", "stt", "time", "player_name", "avatar", "startTimer", "duration", "current", "clearInterval", "durationInMs", "startTime", "Date", "now", "setInterval", "elapsedMs", "remainingMs", "Math", "max", "remainingSec", "parseFloat", "toFixed", "prev", "setExternalTimer", "seconds", "isInitialMount", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTimeStart", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/context/timeListenerContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useRef, useState } from \"react\";\r\nimport { useSounds } from \"./soundContext\";\r\nimport { round } from \"react-placeholder/lib/placeholders\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { useHost } from \"./hostContext\";\r\nimport { useAppDispatch, useAppSelector } from \"../app/store\";\r\nimport { setIsInputDisabled } from \"../app/store/slices/gameSlice\";\r\nimport { gameApi } from \"../shared/services\";\r\n\r\ntype TimeStartContextType = {\r\n  timeLeft: number;\r\n  timeElapsed: number,\r\n  playerAnswerTime: number,\r\n  setPlayerAnswerTime: React.Dispatch<React.SetStateAction<number>>,\r\n  setTimeLeft: React.Dispatch<React.SetStateAction<number>>,\r\n  startTimer: (duration: number) => void;\r\n  setExternalTimer: (seconds: number) => void;\r\n\r\n};\r\n\r\nconst TimeStartContext = createContext<TimeStartContextType | undefined>(undefined);\r\n\r\nexport const TimeStartProvider: React.FC<{ roomId: string; children: React.ReactNode }> = ({\r\n  roomId,\r\n  children,\r\n}) => {\r\n  // const {setAnimationKey} = useHost();\r\n  const dispatch = useAppDispatch()\r\n  const {} = useAppSelector()\r\n  const [timeLeft, setTimeLeft] = useState<number>(0);\r\n  const [timeElapsed, settimeElapsed] = useState<number>(0)\r\n  const [playerAnswerTime, setPlayerAnswerTime] = useState<number>(0)\r\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const sounds = useSounds();\r\n  const [searchParams] = useSearchParams();\r\n  const round = searchParams.get(\"round\") || \"1\";\r\n  const roundRef = useRef(round);\r\n  const {submitAnswer} = gameApi;\r\n\r\n\r\n  const handleTimeEnd = async () => {\r\n    console.log(\"Time is up!\");\r\n    dispatch(setIsInputDisabled(true))\r\n\r\n    \r\n\r\n    await submitAnswer({\r\n      answer: \"test\",\r\n      stt: \"test\",\r\n      time: 1,\r\n      player_name: \"test\",\r\n      avatar: \"test\"\r\n    }, roomId)\r\n  };\r\n\r\n  // Watch for timeLeft reaching 0\r\n  useEffect(() => {\r\n    if (timeLeft === 0) {\r\n      handleTimeEnd();\r\n    }\r\n  }, [timeLeft]);\r\n\r\n  const startTimer = async (duration: number) => {\r\n    // Clear any existing timer\r\n    if (timerRef.current) clearInterval(timerRef.current);\r\n    console.log(\"duration\", duration);\r\n\r\n    const durationInMs = duration * 1000\r\n    // Set the new time\r\n    setTimeLeft(durationInMs / 1000);\r\n    const startTime = Date.now();\r\n\r\n    // Start the countdown\r\n    timerRef.current = setInterval(() => {\r\n      const elapsedMs = Date.now() - startTime;\r\n      const remainingMs = Math.max(durationInMs - elapsedMs, 0);\r\n      const remainingSec = remainingMs / 1000;\r\n\r\n      const timeElapsed = duration - remainingSec; \r\n      console.log(\"timeElapsed\", parseFloat(timeElapsed.toFixed(2)), \"s\");\r\n      settimeElapsed(parseFloat(timeElapsed.toFixed(2)))\r\n      setTimeLeft((prev) => {\r\n        console.log(prev);\r\n        if (prev <= 1) {\r\n          // setAnimationKey((prev: number) => prev + 1);\r\n          clearInterval(timerRef.current!);\r\n          return 0;\r\n        }\r\n        return (prev - 50 / 1000);\r\n      });\r\n    }, 50);\r\n  };\r\n  const setExternalTimer = (seconds: number) => {\r\n    startTimer(seconds);\r\n  };\r\n\r\n  const isInitialMount = useRef(true);\r\n\r\n\r\n\r\n  return (\r\n    <TimeStartContext.Provider value={{ timeLeft, timeElapsed, playerAnswerTime, setPlayerAnswerTime, setTimeLeft, startTimer, setExternalTimer }}>\r\n      {children}\r\n    </TimeStartContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useTimeStart = (): TimeStartContextType => {\r\n  const context = useContext(TimeStartContext);\r\n  if (!context) {\r\n    throw new Error(\"useTimeStart must be used within a TimeStartProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrF,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,eAAe,QAAQ,kBAAkB;AAElD,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa7C,MAAMC,gBAAgB,gBAAGb,aAAa,CAAmCc,SAAS,CAAC;AAEnF,OAAO,MAAMC,iBAA0E,GAAGA,CAAC;EACzFC,MAAM;EACNC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAMC,QAAQ,GAAGZ,cAAc,CAAC,CAAC;EACjC,MAAM,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC;EAC3B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAS,CAAC,CAAC;EACnD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAS,CAAC,CAAC;EACzD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAS,CAAC,CAAC;EACnE,MAAMsB,QAAQ,GAAGvB,MAAM,CAAwB,IAAI,CAAC;EACpD,MAAMwB,MAAM,GAAGtB,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACuB,YAAY,CAAC,GAAGtB,eAAe,CAAC,CAAC;EACxC,MAAMuB,KAAK,GAAGD,YAAY,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EAC9C,MAAMC,QAAQ,GAAG5B,MAAM,CAAC0B,KAAK,CAAC;EAC9B,MAAM;IAACG;EAAY,CAAC,GAAGtB,OAAO;EAG9B,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BhB,QAAQ,CAACV,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAIlC,MAAMuB,YAAY,CAAC;MACjBI,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,MAAM;MACnBC,MAAM,EAAE;IACV,CAAC,EAAExB,MAAM,CAAC;EACZ,CAAC;;EAED;EACAd,SAAS,CAAC,MAAM;IACd,IAAIkB,QAAQ,KAAK,CAAC,EAAE;MAClBa,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACb,QAAQ,CAAC,CAAC;EAEd,MAAMqB,UAAU,GAAG,MAAOC,QAAgB,IAAK;IAC7C;IACA,IAAIhB,QAAQ,CAACiB,OAAO,EAAEC,aAAa,CAAClB,QAAQ,CAACiB,OAAO,CAAC;IACrDT,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEO,QAAQ,CAAC;IAEjC,MAAMG,YAAY,GAAGH,QAAQ,GAAG,IAAI;IACpC;IACArB,WAAW,CAACwB,YAAY,GAAG,IAAI,CAAC;IAChC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;IAE5B;IACAtB,QAAQ,CAACiB,OAAO,GAAGM,WAAW,CAAC,MAAM;MACnC,MAAMC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;MACxC,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACR,YAAY,GAAGK,SAAS,EAAE,CAAC,CAAC;MACzD,MAAMI,YAAY,GAAGH,WAAW,GAAG,IAAI;MAEvC,MAAM7B,WAAW,GAAGoB,QAAQ,GAAGY,YAAY;MAC3CpB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoB,UAAU,CAACjC,WAAW,CAACkC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;MACnEjC,cAAc,CAACgC,UAAU,CAACjC,WAAW,CAACkC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAClDnC,WAAW,CAAEoC,IAAI,IAAK;QACpBvB,OAAO,CAACC,GAAG,CAACsB,IAAI,CAAC;QACjB,IAAIA,IAAI,IAAI,CAAC,EAAE;UACb;UACAb,aAAa,CAAClB,QAAQ,CAACiB,OAAQ,CAAC;UAChC,OAAO,CAAC;QACV;QACA,OAAQc,IAAI,GAAG,EAAE,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,MAAMC,gBAAgB,GAAIC,OAAe,IAAK;IAC5ClB,UAAU,CAACkB,OAAO,CAAC;EACrB,CAAC;EAED,MAAMC,cAAc,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAInC,oBACES,OAAA,CAACC,gBAAgB,CAACgD,QAAQ;IAACC,KAAK,EAAE;MAAE1C,QAAQ;MAAEE,WAAW;MAAEE,gBAAgB;MAAEC,mBAAmB;MAAEJ,WAAW;MAAEoB,UAAU;MAAEiB;IAAiB,CAAE;IAAAzC,QAAA,EAC3IA;EAAQ;IAAA8C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAAChD,EAAA,CAnFWH,iBAA0E;EAAA,QAKpER,cAAc,EACpBC,cAAc,EAKVH,SAAS,EACDC,eAAe;AAAA;AAAA6D,EAAA,GAZ3BpD,iBAA0E;AAqFvF,OAAO,MAAMqD,YAAY,GAAGA,CAAA,KAA4B;EAAAC,GAAA;EACtD,MAAMC,OAAO,GAAGrE,UAAU,CAACY,gBAAgB,CAAC;EAC5C,IAAI,CAACyD,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,YAAY;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}