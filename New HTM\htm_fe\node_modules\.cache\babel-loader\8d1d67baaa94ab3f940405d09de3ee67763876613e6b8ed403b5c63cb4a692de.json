{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\context\\\\timeListenerContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useRef, useState } from \"react\";\nimport { useSounds } from \"./soundContext\";\nimport { useSearchParams } from \"react-router-dom\";\nimport { useAppDispatch } from \"../app/store\";\nimport { setIsInputDisabled } from \"../app/store/slices/gameSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TimeStartContext = /*#__PURE__*/createContext(undefined);\nexport const TimeStartProvider = ({\n  roomId,\n  children\n}) => {\n  _s();\n  // const {setAnimationKey} = useHost();\n  const dispatch = useAppDispatch();\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [timeElapsed, settimeElapsed] = useState(0);\n  const [playerAnswerTime, setPlayerAnswerTime] = useState(0);\n  const timerRef = useRef(null);\n  const sounds = useSounds();\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n  const roundRef = useRef(round);\n  const handleTimeEnd = () => {\n    console.log(\"Time is up!\");\n    dispatch(setIsInputDisabled(true));\n  };\n\n  // Watch for timeLeft reaching 0\n  useEffect(() => {\n    if (timeLeft === 0) {\n      handleTimeEnd();\n    }\n  }, [timeLeft]);\n  const startTimer = async duration => {\n    // Clear any existing timer\n    if (timerRef.current) clearInterval(timerRef.current);\n    console.log(\"duration\", duration);\n    const durationInMs = duration * 1000;\n    // Set the new time\n    setTimeLeft(durationInMs / 1000);\n    const startTime = Date.now();\n\n    // Start the countdown\n    timerRef.current = setInterval(() => {\n      const elapsedMs = Date.now() - startTime;\n      const remainingMs = Math.max(durationInMs - elapsedMs, 0);\n      const remainingSec = remainingMs / 1000;\n      const timeElapsed = duration - remainingSec;\n      console.log(\"timeElapsed\", parseFloat(timeElapsed.toFixed(2)), \"s\");\n      settimeElapsed(parseFloat(timeElapsed.toFixed(2)));\n      setTimeLeft(prev => {\n        console.log(prev);\n        if (prev <= 1) {\n          // setAnimationKey((prev: number) => prev + 1);\n          clearInterval(timerRef.current);\n          return 0;\n        }\n        return prev - 50 / 1000;\n      });\n    }, 50);\n  };\n  const setExternalTimer = seconds => {\n    startTimer(seconds);\n  };\n  const isInitialMount = useRef(true);\n  return /*#__PURE__*/_jsxDEV(TimeStartContext.Provider, {\n    value: {\n      timeLeft,\n      timeElapsed,\n      playerAnswerTime,\n      setPlayerAnswerTime,\n      setTimeLeft,\n      startTimer,\n      setExternalTimer\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(TimeStartProvider, \"XIpe5UX8QEBDnXIb+w9P1c9dqvA=\", false, function () {\n  return [useAppDispatch, useSounds, useSearchParams];\n});\n_c = TimeStartProvider;\nexport const useTimeStart = () => {\n  _s2();\n  const context = useContext(TimeStartContext);\n  if (!context) {\n    throw new Error(\"useTimeStart must be used within a TimeStartProvider\");\n  }\n  return context;\n};\n_s2(useTimeStart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TimeStartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useRef", "useState", "useSounds", "useSearchParams", "useAppDispatch", "setIsInputDisabled", "jsxDEV", "_jsxDEV", "TimeStartContext", "undefined", "TimeStartProvider", "roomId", "children", "_s", "dispatch", "timeLeft", "setTimeLeft", "timeElapsed", "settimeElapsed", "playerAnswerTime", "setPlayerAnswerTime", "timerRef", "sounds", "searchParams", "round", "get", "roundRef", "handleTimeEnd", "console", "log", "startTimer", "duration", "current", "clearInterval", "durationInMs", "startTime", "Date", "now", "setInterval", "elapsedMs", "remainingMs", "Math", "max", "remainingSec", "parseFloat", "toFixed", "prev", "setExternalTimer", "seconds", "isInitialMount", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTimeStart", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/context/timeListenerContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useRef, useState } from \"react\";\r\nimport { useSounds } from \"./soundContext\";\r\nimport { round } from \"react-placeholder/lib/placeholders\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { useHost } from \"./hostContext\";\r\nimport { useAppDispatch } from \"../app/store\";\r\nimport { setIsInputDisabled } from \"../app/store/slices/gameSlice\";\r\nimport { gameApi } from \"../shared/services\";\r\n\r\ntype TimeStartContextType = {\r\n  timeLeft: number;\r\n  timeElapsed: number,\r\n  playerAnswerTime: number,\r\n  setPlayerAnswerTime: React.Dispatch<React.SetStateAction<number>>,\r\n  setTimeLeft: React.Dispatch<React.SetStateAction<number>>,\r\n  startTimer: (duration: number) => void;\r\n  setExternalTimer: (seconds: number) => void;\r\n\r\n};\r\n\r\nconst TimeStartContext = createContext<TimeStartContextType | undefined>(undefined);\r\n\r\nexport const TimeStartProvider: React.FC<{ roomId: string; children: React.ReactNode }> = ({\r\n  roomId,\r\n  children,\r\n}) => {\r\n  // const {setAnimationKey} = useHost();\r\n  const dispatch = useAppDispatch()\r\n  const [timeLeft, setTimeLeft] = useState<number>(0);\r\n  const [timeElapsed, settimeElapsed] = useState<number>(0)\r\n  const [playerAnswerTime, setPlayerAnswerTime] = useState<number>(0)\r\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const sounds = useSounds();\r\n  const [searchParams] = useSearchParams();\r\n  const round = searchParams.get(\"round\") || \"1\";\r\n  const roundRef = useRef(round);\r\n\r\n\r\n  const handleTimeEnd = () => {\r\n    console.log(\"Time is up!\");\r\n    dispatch(setIsInputDisabled(true))\r\n  };\r\n\r\n  // Watch for timeLeft reaching 0\r\n  useEffect(() => {\r\n    if (timeLeft === 0) {\r\n      handleTimeEnd();\r\n    }\r\n  }, [timeLeft]);\r\n\r\n  const startTimer = async (duration: number) => {\r\n    // Clear any existing timer\r\n    if (timerRef.current) clearInterval(timerRef.current);\r\n    console.log(\"duration\", duration);\r\n\r\n    const durationInMs = duration * 1000\r\n    // Set the new time\r\n    setTimeLeft(durationInMs / 1000);\r\n    const startTime = Date.now();\r\n\r\n    // Start the countdown\r\n    timerRef.current = setInterval(() => {\r\n      const elapsedMs = Date.now() - startTime;\r\n      const remainingMs = Math.max(durationInMs - elapsedMs, 0);\r\n      const remainingSec = remainingMs / 1000;\r\n\r\n      const timeElapsed = duration - remainingSec; \r\n      console.log(\"timeElapsed\", parseFloat(timeElapsed.toFixed(2)), \"s\");\r\n      settimeElapsed(parseFloat(timeElapsed.toFixed(2)))\r\n      setTimeLeft((prev) => {\r\n        console.log(prev);\r\n        if (prev <= 1) {\r\n          // setAnimationKey((prev: number) => prev + 1);\r\n          clearInterval(timerRef.current!);\r\n          return 0;\r\n        }\r\n        return (prev - 50 / 1000);\r\n      });\r\n    }, 50);\r\n  };\r\n  const setExternalTimer = (seconds: number) => {\r\n    startTimer(seconds);\r\n  };\r\n\r\n  const isInitialMount = useRef(true);\r\n\r\n\r\n\r\n  return (\r\n    <TimeStartContext.Provider value={{ timeLeft, timeElapsed, playerAnswerTime, setPlayerAnswerTime, setTimeLeft, startTimer, setExternalTimer }}>\r\n      {children}\r\n    </TimeStartContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useTimeStart = (): TimeStartContextType => {\r\n  const context = useContext(TimeStartContext);\r\n  if (!context) {\r\n    throw new Error(\"useTimeStart must be used within a TimeStartProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrF,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,eAAe,QAAQ,kBAAkB;AAElD,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,kBAAkB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAcnE,MAAMC,gBAAgB,gBAAGX,aAAa,CAAmCY,SAAS,CAAC;AAEnF,OAAO,MAAMC,iBAA0E,GAAGA,CAAC;EACzFC,MAAM;EACNC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAMC,QAAQ,GAAGV,cAAc,CAAC,CAAC;EACjC,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAS,CAAC,CAAC;EACnD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAS,CAAC,CAAC;EACzD,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAS,CAAC,CAAC;EACnE,MAAMoB,QAAQ,GAAGrB,MAAM,CAAwB,IAAI,CAAC;EACpD,MAAMsB,MAAM,GAAGpB,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACqB,YAAY,CAAC,GAAGpB,eAAe,CAAC,CAAC;EACxC,MAAMqB,KAAK,GAAGD,YAAY,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EAC9C,MAAMC,QAAQ,GAAG1B,MAAM,CAACwB,KAAK,CAAC;EAG9B,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1BC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1Bf,QAAQ,CAACT,kBAAkB,CAAC,IAAI,CAAC,CAAC;EACpC,CAAC;;EAED;EACAN,SAAS,CAAC,MAAM;IACd,IAAIgB,QAAQ,KAAK,CAAC,EAAE;MAClBY,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMe,UAAU,GAAG,MAAOC,QAAgB,IAAK;IAC7C;IACA,IAAIV,QAAQ,CAACW,OAAO,EAAEC,aAAa,CAACZ,QAAQ,CAACW,OAAO,CAAC;IACrDJ,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEE,QAAQ,CAAC;IAEjC,MAAMG,YAAY,GAAGH,QAAQ,GAAG,IAAI;IACpC;IACAf,WAAW,CAACkB,YAAY,GAAG,IAAI,CAAC;IAChC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;;IAE5B;IACAhB,QAAQ,CAACW,OAAO,GAAGM,WAAW,CAAC,MAAM;MACnC,MAAMC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;MACxC,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACR,YAAY,GAAGK,SAAS,EAAE,CAAC,CAAC;MACzD,MAAMI,YAAY,GAAGH,WAAW,GAAG,IAAI;MAEvC,MAAMvB,WAAW,GAAGc,QAAQ,GAAGY,YAAY;MAC3Cf,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEe,UAAU,CAAC3B,WAAW,CAAC4B,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;MACnE3B,cAAc,CAAC0B,UAAU,CAAC3B,WAAW,CAAC4B,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD7B,WAAW,CAAE8B,IAAI,IAAK;QACpBlB,OAAO,CAACC,GAAG,CAACiB,IAAI,CAAC;QACjB,IAAIA,IAAI,IAAI,CAAC,EAAE;UACb;UACAb,aAAa,CAACZ,QAAQ,CAACW,OAAQ,CAAC;UAChC,OAAO,CAAC;QACV;QACA,OAAQc,IAAI,GAAG,EAAE,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,MAAMC,gBAAgB,GAAIC,OAAe,IAAK;IAC5ClB,UAAU,CAACkB,OAAO,CAAC;EACrB,CAAC;EAED,MAAMC,cAAc,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAInC,oBACEO,OAAA,CAACC,gBAAgB,CAAC0C,QAAQ;IAACC,KAAK,EAAE;MAAEpC,QAAQ;MAAEE,WAAW;MAAEE,gBAAgB;MAAEC,mBAAmB;MAAEJ,WAAW;MAAEc,UAAU;MAAEiB;IAAiB,CAAE;IAAAnC,QAAA,EAC3IA;EAAQ;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAAC1C,EAAA,CAvEWH,iBAA0E;EAAA,QAKpEN,cAAc,EAKhBF,SAAS,EACDC,eAAe;AAAA;AAAAqD,EAAA,GAX3B9C,iBAA0E;AAyEvF,OAAO,MAAM+C,YAAY,GAAGA,CAAA,KAA4B;EAAAC,GAAA;EACtD,MAAMC,OAAO,GAAG7D,UAAU,CAACU,gBAAgB,CAAC;EAC5C,IAAI,CAACmD,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,YAAY;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}