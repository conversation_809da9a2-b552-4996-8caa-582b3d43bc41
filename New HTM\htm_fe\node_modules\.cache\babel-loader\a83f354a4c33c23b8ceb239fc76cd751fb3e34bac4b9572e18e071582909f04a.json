{"ast": null, "code": "// Room API service\nimport { api } from '../api/client';\nimport { API_ENDPOINTS } from '../../constants';\nexport const roomApi = {\n  /**\r\n   * Get all available rooms\r\n   */\n  async getRooms() {\n    const response = await api.get(API_ENDPOINTS.ROOM.BASE);\n    return response.data.data;\n  },\n  /**\r\n   * Get rooms by user ID\r\n   */\n  async getRoomsByUserId() {\n    const response = await api.get(`${API_ENDPOINTS.ROOM.BASE}/user`);\n    return response.data.data;\n  },\n  /**\r\n   * Create a new room\r\n   */\n  async createRoom(roomData) {\n    const response = await api.post(API_ENDPOINTS.ROOM.BASE, roomData);\n    return response.data.data;\n  },\n  /**\r\n   * Validate room before joining\r\n   */\n  async validateRoom(params) {\n    const response = await api.post(API_ENDPOINTS.ROOM.VALIDATE, params);\n    return response.data.data;\n  },\n  /**\r\n   * Join a room as player\r\n   */\n  async joinRoom(joinData) {\n    const response = await api.post(API_ENDPOINTS.ROOM.JOIN, joinData);\n    return response.data.data;\n  },\n  /**\r\n   * Join a room as spectator\r\n   */\n  async joinAsSpectator(spectatorData) {\n    const response = await api.post(`${API_ENDPOINTS.ROOM.BASE}${API_ENDPOINTS.ROOM.SPECTATOR}`, spectatorData);\n    return response.data.data;\n  },\n  /**\r\n   * Leave a room\r\n   */\n  async leaveRoom(roomId) {\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}${API_ENDPOINTS.ROOM.LEAVE}`);\n  },\n  /**\r\n   * Update room settings (host only)\r\n   */\n  async updateRoom(roomId, updates) {\n    const response = await api.patch(`${API_ENDPOINTS.ROOM.BASE}/${roomId}`, updates);\n    return response.data.data;\n  },\n  /**\r\n   * Delete a room (host only)\r\n   */\n  async deleteRoom(roomId) {\n    await api.delete(`${API_ENDPOINTS.ROOM.BASE}/${roomId}`);\n  },\n  /**\r\n   * Get room details\r\n   */\n  async getRoomDetails(roomId) {\n    const response = await api.get(`${API_ENDPOINTS.ROOM.BASE}/${roomId}`);\n    return response.data.data;\n  },\n  /**\r\n   * Kick player from room (host only)\r\n   */\n  async kickPlayer(roomId, playerId) {\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}/kick`, {\n      playerId\n    });\n  },\n  /**\r\n   * Start game in room (host only)\r\n   */\n  async startGame(roomId) {\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}/start`);\n  },\n  /**\r\n   * End game in room (host only)\r\n   */\n  async endGame(roomId) {\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}/end`);\n  }\n};\nexport default roomApi;", "map": {"version": 3, "names": ["api", "API_ENDPOINTS", "roomApi", "getRooms", "response", "get", "ROOM", "BASE", "data", "getRoomsByUserId", "createRoom", "roomData", "post", "validateRoom", "params", "VALIDATE", "joinRoom", "joinData", "JOIN", "joinAsSpectator", "spectatorData", "SPECTATOR", "leaveRoom", "roomId", "LEAVE", "updateRoom", "updates", "patch", "deleteRoom", "delete", "getRoomDetails", "kickPlayer", "playerId", "startGame", "endGame"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/services/room/roomApi.ts"], "sourcesContent": ["// Room API service\r\nimport { api } from '../api/client';\r\nimport { API_ENDPOINTS } from '../../constants';\r\nimport { \r\n  CreateRoomRequest,\r\n  CreateRoomResponse,\r\n  JoinRoomRequest,\r\n  JoinRoomResponse,\r\n  GetRoomsResponse,\r\n  RoomValidationRequest,\r\n  RoomValidationResponse,\r\n  SpectatorJoinRequest,\r\n  Room\r\n} from '../../types';\r\n\r\nexport const roomApi = {\r\n  /**\r\n   * Get all available rooms\r\n   */\r\n  async getRooms(): Promise<Room[]> {\r\n    const response = await api.get<Room[]>(API_ENDPOINTS.ROOM.BASE);\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Get rooms by user ID\r\n   */\r\n  async getRoomsByUserId(): Promise<Room[]> {\r\n    const response = await api.get<Room[]>(`${API_ENDPOINTS.ROOM.BASE}/user`);\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Create a new room\r\n   */\r\n  async createRoom(roomData: CreateRoomRequest): Promise<Room> {\r\n    const response = await api.post<Room>(API_ENDPOINTS.ROOM.BASE, roomData);\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Validate room before joining\r\n   */\r\n  async validateRoom(params: RoomValidationRequest): Promise<RoomValidationResponse> {\r\n    const response = await api.post<RoomValidationResponse>(\r\n      API_ENDPOINTS.ROOM.VALIDATE,\r\n      params\r\n    );\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Join a room as player\r\n   */\r\n  async joinRoom(joinData: JoinRoomRequest): Promise<{ room: Room; accessToken: string }> {\r\n    const response = await api.post<{ room: Room; accessToken: string }>(\r\n      API_ENDPOINTS.ROOM.JOIN,\r\n      joinData\r\n    );\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Join a room as spectator\r\n   */\r\n  async joinAsSpectator(spectatorData: SpectatorJoinRequest): Promise<{ room: Room; accessToken: string }> {\r\n    const response = await api.post<{ room: Room; accessToken: string }>(\r\n      `${API_ENDPOINTS.ROOM.BASE}${API_ENDPOINTS.ROOM.SPECTATOR}`,\r\n      spectatorData\r\n    );\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Leave a room\r\n   */\r\n  async leaveRoom(roomId: string): Promise<void> {\r\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}${API_ENDPOINTS.ROOM.LEAVE}`);\r\n  },\r\n\r\n  /**\r\n   * Update room settings (host only)\r\n   */\r\n  async updateRoom(roomId: string, updates: Partial<Room>): Promise<Room> {\r\n    const response = await api.patch<Room>(`${API_ENDPOINTS.ROOM.BASE}/${roomId}`, updates);\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Delete a room (host only)\r\n   */\r\n  async deleteRoom(roomId: string): Promise<void> {\r\n    await api.delete(`${API_ENDPOINTS.ROOM.BASE}/${roomId}`);\r\n  },\r\n\r\n  /**\r\n   * Get room details\r\n   */\r\n  async getRoomDetails(roomId: string): Promise<Room> {\r\n    const response = await api.get<Room>(`${API_ENDPOINTS.ROOM.BASE}/${roomId}`);\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Kick player from room (host only)\r\n   */\r\n  async kickPlayer(roomId: string, playerId: string): Promise<void> {\r\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}/kick`, { playerId });\r\n  },\r\n\r\n  /**\r\n   * Start game in room (host only)\r\n   */\r\n  async startGame(roomId: string): Promise<void> {\r\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}/start`);\r\n  },\r\n\r\n  /**\r\n   * End game in room (host only)\r\n   */\r\n  async endGame(roomId: string): Promise<void> {\r\n    await api.post(`${API_ENDPOINTS.ROOM.BASE}/${roomId}/end`);\r\n  },\r\n};\r\n\r\nexport default roomApi;\r\n"], "mappings": "AAAA;AACA,SAASA,GAAG,QAAQ,eAAe;AACnC,SAASC,aAAa,QAAQ,iBAAiB;AAa/C,OAAO,MAAMC,OAAO,GAAG;EACrB;AACF;AACA;EACE,MAAMC,QAAQA,CAAA,EAAoB;IAChC,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAASJ,aAAa,CAACK,IAAI,CAACC,IAAI,CAAC;IAC/D,OAAOH,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMC,gBAAgBA,CAAA,EAAoB;IACxC,MAAML,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAS,GAAGJ,aAAa,CAACK,IAAI,CAACC,IAAI,OAAO,CAAC;IACzE,OAAOH,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAME,UAAUA,CAACC,QAA2B,EAAiB;IAC3D,MAAMP,QAAQ,GAAG,MAAMJ,GAAG,CAACY,IAAI,CAAOX,aAAa,CAACK,IAAI,CAACC,IAAI,EAAEI,QAAQ,CAAC;IACxE,OAAOP,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMK,YAAYA,CAACC,MAA6B,EAAmC;IACjF,MAAMV,QAAQ,GAAG,MAAMJ,GAAG,CAACY,IAAI,CAC7BX,aAAa,CAACK,IAAI,CAACS,QAAQ,EAC3BD,MACF,CAAC;IACD,OAAOV,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMQ,QAAQA,CAACC,QAAyB,EAAgD;IACtF,MAAMb,QAAQ,GAAG,MAAMJ,GAAG,CAACY,IAAI,CAC7BX,aAAa,CAACK,IAAI,CAACY,IAAI,EACvBD,QACF,CAAC;IACD,OAAOb,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMW,eAAeA,CAACC,aAAmC,EAAgD;IACvG,MAAMhB,QAAQ,GAAG,MAAMJ,GAAG,CAACY,IAAI,CAC7B,GAAGX,aAAa,CAACK,IAAI,CAACC,IAAI,GAAGN,aAAa,CAACK,IAAI,CAACe,SAAS,EAAE,EAC3DD,aACF,CAAC;IACD,OAAOhB,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMc,SAASA,CAACC,MAAc,EAAiB;IAC7C,MAAMvB,GAAG,CAACY,IAAI,CAAC,GAAGX,aAAa,CAACK,IAAI,CAACC,IAAI,IAAIgB,MAAM,GAAGtB,aAAa,CAACK,IAAI,CAACkB,KAAK,EAAE,CAAC;EACnF,CAAC;EAED;AACF;AACA;EACE,MAAMC,UAAUA,CAACF,MAAc,EAAEG,OAAsB,EAAiB;IACtE,MAAMtB,QAAQ,GAAG,MAAMJ,GAAG,CAAC2B,KAAK,CAAO,GAAG1B,aAAa,CAACK,IAAI,CAACC,IAAI,IAAIgB,MAAM,EAAE,EAAEG,OAAO,CAAC;IACvF,OAAOtB,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMoB,UAAUA,CAACL,MAAc,EAAiB;IAC9C,MAAMvB,GAAG,CAAC6B,MAAM,CAAC,GAAG5B,aAAa,CAACK,IAAI,CAACC,IAAI,IAAIgB,MAAM,EAAE,CAAC;EAC1D,CAAC;EAED;AACF;AACA;EACE,MAAMO,cAAcA,CAACP,MAAc,EAAiB;IAClD,MAAMnB,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAO,GAAGJ,aAAa,CAACK,IAAI,CAACC,IAAI,IAAIgB,MAAM,EAAE,CAAC;IAC5E,OAAOnB,QAAQ,CAACI,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMuB,UAAUA,CAACR,MAAc,EAAES,QAAgB,EAAiB;IAChE,MAAMhC,GAAG,CAACY,IAAI,CAAC,GAAGX,aAAa,CAACK,IAAI,CAACC,IAAI,IAAIgB,MAAM,OAAO,EAAE;MAAES;IAAS,CAAC,CAAC;EAC3E,CAAC;EAED;AACF;AACA;EACE,MAAMC,SAASA,CAACV,MAAc,EAAiB;IAC7C,MAAMvB,GAAG,CAACY,IAAI,CAAC,GAAGX,aAAa,CAACK,IAAI,CAACC,IAAI,IAAIgB,MAAM,QAAQ,CAAC;EAC9D,CAAC;EAED;AACF;AACA;EACE,MAAMW,OAAOA,CAACX,MAAc,EAAiB;IAC3C,MAAMvB,GAAG,CAACY,IAAI,CAAC,GAAGX,aAAa,CAACK,IAAI,CAACC,IAAI,IAAIgB,MAAM,MAAM,CAAC;EAC5D;AACF,CAAC;AAED,eAAerB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}